package islamic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/api/common"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/do"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"
	token "halalplus/utility/token"
	"sort"
	"strconv"
	"time"
)

type (
	sIslamic struct {
		//signInMsgChan        chan *model.SignInLogInput
		//attrsBatchUpdateChan chan *model.AttrsToUpdate
		//attrsNoDelay<PERSON>han     chan *model.AttrsToUpdate
		//quit                 chan struct{}
		//
		//ConfigCaptcha    string
		//signInRecord<PERSON>han chan *do.UserSigninLog
		//accountSet       *bloom.Filter
		//transferSet      *bloom.Filter
		//phoneSet         *bloom.Filter
	}
)

func init() {
	service.RegisterIslamic(New())
}

func New() service.IIslamic {
	u := &sIslamic{
		//signInMsgChan:        make(chan *model.SignInLogInput, 1000),
		//attrsBatchUpdateChan: make(chan *model.AttrsToUpdate, 10000),
		//attrsNoDelayChan:     make(chan *model.AttrsToUpdate, 100),
		//
		//signInRecordChan: make(chan *do.UserSigninLog, 1000),
		//quit:             make(chan struct{}, 1),
	}
	//u.cronJobCreateSignInLog()
	//u.cronJobUpdateUserAttrs()
	//u.cronJobUpdateUserAttrsNoDelay()

	//u.ConfigCaptcha = u.getSmsOptConfig(context.Background())

	//g.Go(gctx.New(), func(ctx context.Context) {
	//	time.Sleep(3 * time.Second)
	//	// u.convertTopLevelDomain(ctx)
	//}, nil)
	return u
}
func (s *sIslamic) setDefaultPage(ctx context.Context, in **common.PageRequest) {
	if *in == nil {
		*in = &common.PageRequest{
			Page: 1,
			Size: 10,
		}
	}
	if (*in).Page <= 0 {
		(*in).Page = 1
	}
	if (*in).Size <= 0 {
		(*in).Size = 10
	}
}

func (s *sIslamic) WiridList(ctx context.Context, in *v1.WiridListReq) (out *v1.WiridListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.WiridListRes{
		Data: &v1.WiridListResData{
			List: make([]*v1.WiridInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var wiridList []entity.NewsWirid
	query := dao.NewsWirid.Ctx(ctx)
	if in.Name != "" {
		query = query.WhereLike(dao.NewsWirid.Columns().Name, "%"+in.Name+"%")
	}
	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id asc").Scan(&wiridList)
	if err != nil || len(wiridList) == 0 {
		return out
	}
	dataList := make([]*v1.WiridInfo, 0, len(wiridList))
	for _, tahlil := range wiridList {
		transfer := &v1.WiridInfo{}
		gconv.Struct(tahlil, transfer)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList

	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) WiridBacaanList(ctx context.Context, in *v1.WiridBacaanListReq) (out *v1.WiridBacaanListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.WiridBacaanListRes{
		Data: &v1.WiridBacaanListResData{
			List: make([]*v1.WiridBacaanList, 0),
			Page: &common.PageResponse{},
		},
	}
	var wiridBacaanList []entity.NewsWiridBacaan
	query := dao.NewsWiridBacaan.Ctx(ctx).Where(dao.NewsWiridBacaan.Columns().WiridId, in.WiridId).Where(dao.NewsWiridBacaan.Columns().Pid, 0)

	if in.Name != "" {
		query = query.WhereLike(dao.NewsWiridBacaan.Columns().Name, "%"+in.Name+"%")
	}

	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id asc").Scan(&wiridBacaanList)
	if err != nil || len(wiridBacaanList) == 0 {
		return out
	}
	dataList := make([]*v1.WiridBacaanList, 0, len(wiridBacaanList))
	for key, tahlil := range wiridBacaanList {
		transfer := &v1.WiridBacaanList{}
		gconv.Struct(tahlil, transfer)
		transfer.BacaanId = uint32(tahlil.Id)
		transfer.Number = uint32(key + 1)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) WiridBacaanInfoList(ctx context.Context, in *v1.WiridBacaanInfoListReq) (out *v1.WiridBacaanInfoListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.WiridBacaanInfoListRes{
		Data: &v1.WiridBacaanInfoListResData{
			List: make([]*v1.WiridBacaanInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var wiridBacaanList []entity.NewsWiridBacaan
	query := dao.NewsWiridBacaan.Ctx(ctx).Where(dao.NewsWiridBacaan.Columns().Pid, in.BacaanId)

	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id asc").Scan(&wiridBacaanList)
	if err != nil || len(wiridBacaanList) == 0 {
		return out
	}
	dataList := make([]*v1.WiridBacaanInfo, 0, len(wiridBacaanList))
	for _, tahlil := range wiridBacaanList {
		transfer := &v1.WiridBacaanInfo{}
		gconv.Struct(tahlil, transfer)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) DoaList(ctx context.Context, in *v1.DoaListReq) (out *v1.DoaListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.DoaListRes{
		Data: &v1.DoaListResData{
			List: make([]*v1.DoaInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var doaList []entity.NewsDoa
	query := dao.NewsDoa.Ctx(ctx)
	if in.Name != "" {
		query = query.WhereLike(dao.NewsDoa.Columns().Name, "%"+in.Name+"%")
	}
	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id asc").Scan(&doaList)
	if err != nil || len(doaList) == 0 {
		return out
	}
	dataList := make([]*v1.DoaInfo, 0, len(doaList))
	for _, tahlil := range doaList {
		transfer := &v1.DoaInfo{}
		gconv.Struct(tahlil, transfer)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList

	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) DoaBacaanList(ctx context.Context, in *v1.DoaBacaanListReq) (out *v1.DoaBacaanListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.DoaBacaanListRes{
		Data: &v1.DoaBacaanListResData{
			List: make([]*v1.DoaBacaanList, 0),
			Page: &common.PageResponse{},
		},
	}
	var doaBacaanList []entity.NewsDoaBacaan
	query := dao.NewsDoaBacaan.Ctx(ctx).Where(dao.NewsDoaBacaan.Columns().DoaId, in.DoaId).Where(dao.NewsDoaBacaan.Columns().Pid, 0)

	if in.Name != "" {
		query = query.WhereLike(dao.NewsDoaBacaan.Columns().Name, "%"+in.Name+"%")
	}

	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id asc").Scan(&doaBacaanList)
	if err != nil || len(doaBacaanList) == 0 {
		return out
	}
	dataList := make([]*v1.DoaBacaanList, 0, len(doaBacaanList))
	for key, tahlil := range doaBacaanList {
		transfer := &v1.DoaBacaanList{}
		gconv.Struct(tahlil, transfer)
		transfer.BacaanId = uint32(tahlil.Id)
		transfer.Number = uint32(key + 1)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) DoaBacaanInfoList(ctx context.Context, in *v1.DoaBacaanInfoListReq) (out *v1.DoaBacaanInfoListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.DoaBacaanInfoListRes{
		Data: &v1.DoaBacaanInfoListResData{
			List: make([]*v1.DoaBacaanInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var doaBacaanList []entity.NewsDoaBacaan
	query := dao.NewsDoaBacaan.Ctx(ctx).Where(dao.NewsDoaBacaan.Columns().Pid, in.BacaanId)

	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id asc").Scan(&doaBacaanList)
	if err != nil || len(doaBacaanList) == 0 {
		return out
	}
	dataList := make([]*v1.DoaBacaanInfo, 0, len(doaBacaanList))
	for _, tahlil := range doaBacaanList {
		transfer := &v1.DoaBacaanInfo{}
		gconv.Struct(tahlil, transfer)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) TahlilList(ctx context.Context, in *v1.TahlilListReq) (out *v1.TahlilListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.TahlilListRes{
		Data: &v1.TahlilListResData{
			List: make([]*v1.NewsTahlilInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var tahlilList []entity.NewsTahlil
	query := dao.NewsTahlil.Ctx(ctx)
	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&tahlilList)
	if err != nil || len(tahlilList) == 0 {
		return out
	}
	dataList := make([]*v1.NewsTahlilInfo, 0, len(tahlilList))
	for _, tahlil := range tahlilList {
		transfer := &v1.NewsTahlilInfo{}
		gconv.Struct(tahlil, transfer)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList

	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) SurahList(ctx context.Context, in *v1.SurahListReq) (out *v1.SurahListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.SurahListRes{
		Data: &v1.SurahListResData{
			List: make([]*v1.SuratDaftarInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var quranList []entity.SuratDaftar
	query := dao.SuratDaftar.Ctx(ctx)
	if in.Id > 0 {
		query = query.Where(dao.SuratDaftar.Columns().Id, in.Id)
	}
	if in.IsPopular > 0 {
		query = query.Where(dao.SuratDaftar.Columns().IsPopular, in.IsPopular)
	}
	if in.Name != "" {
		query = query.WhereLike(dao.SuratDaftar.Columns().NamaLatin, "%"+in.Name+"%")
	}
	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&quranList)
	if err != nil || len(quranList) == 0 {
		return out
	}
	dataList := make([]*v1.SuratDaftarInfo, 0, len(quranList))
	for _, quran := range quranList {
		transfer := &v1.SuratDaftarInfo{}
		gconv.Struct(quran, transfer)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList

	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) JuzList(ctx context.Context, in *model.JuzParamInput) (out []*model.JuzParamOutput) {

	var surahList []*entity.SuratDaftar
	querySurah := dao.SuratDaftar.Ctx(ctx)
	errSurah := querySurah.Scan(&surahList)
	if errSurah != nil || len(surahList) == 0 {
		return out
	}
	// 初始化一个map 获取所有的SurahId和SurahName
	surahMap := make(map[int]*model.SurahParamOutput)
	for _, surah := range surahList {
		surahMap[surah.Id] = &model.SurahParamOutput{
			Name:      surah.Nama,
			NameLatin: surah.NamaLatin,
		}
	}

	var ayahList []*entity.SuratAyat
	query := dao.SuratAyat.Ctx(ctx).WhereGT(dao.SuratAyat.Columns().Juz, 0)

	if in.Name != "" {
	}
	err := query.Scan(&ayahList)
	if err != nil || len(ayahList) == 0 {
		return out
	}

	//根据quran.Juz进行分组
	juzMap := make(map[int]*v1.JuzInfo)
	for _, quran := range ayahList {
		if _, ok := juzMap[quran.Juz]; !ok {
			juzMap[quran.Juz] = &v1.JuzInfo{
				StartSurahId:   uint32(quran.SurahId),
				StartSurahName: surahMap[quran.SurahId].NameLatin,
				EndSurahId:     uint32(quran.SurahId),
				EndSurahName:   surahMap[quran.SurahId].NameLatin,
				StartAyahId:    uint32(quran.AyatId),
				EndAyahId:      uint32(quran.AyatId),
				FirstWord:      quran.Ar,
			}
		} else {
			EndSurahName := surahMap[quran.SurahId].NameLatin
			//更新EndSurahId和EndAyahId
			juzMap[quran.Juz].EndSurahId = uint32(quran.SurahId)
			juzMap[quran.Juz].EndSurahName = EndSurahName
			juzMap[quran.Juz].EndAyahId = uint32(quran.AyatId)
		}
	}
	//juzMap 排序
	for key, juz := range juzMap {
		out = append(out, &model.JuzParamOutput{
			StartSurahId:   uint(juz.StartSurahId),
			StartSurahName: juz.StartSurahName,
			EndSurahId:     uint(juz.EndSurahId),
			EndSurahName:   juz.EndSurahName,
			StartAyahId:    uint(juz.StartAyahId),
			EndAyahId:      uint(juz.EndAyahId),
			Name:           "Juz " + strconv.Itoa(key), // 这里可以根据需要设置Juz的名称
			FirstWord:      juz.FirstWord,
		})
	}
	return out
}

func (s *sIslamic) QuerySurahByAyahId(ayahId int) (out *entity.SuratDaftar) {
	// 查询SurahId
	var surahAyah *entity.SuratAyat
	err := dao.SuratAyat.Ctx(context.Background()).Where(dao.SuratAyat.Columns().Id, ayahId).Scan(&surahAyah)
	if err != nil || surahAyah == nil {
		return nil
	}
	// 查询Surah信息
	var surah *entity.SuratDaftar
	err = dao.SuratDaftar.Ctx(context.Background()).Where(dao.SuratDaftar.Columns().Id, surahAyah.SurahId).Scan(&surah)
	if err != nil || surah == nil {
		return nil
	}
	return surah
}

func (s *sIslamic) QueryAyahByAyahId(ayahId int) (out *entity.SuratAyat) {
	// 查询SurahId
	var surahAyah *entity.SuratAyat
	err := dao.SuratAyat.Ctx(context.Background()).Where(dao.SuratAyat.Columns().Id, ayahId).Scan(&surahAyah)
	if err != nil || surahAyah == nil {
		return nil
	}
	return surahAyah
}

func (s *sIslamic) AyahList(ctx context.Context, in *v1.AyahListReq) (out *v1.AyahListRes) {

	s.setDefaultPage(ctx, &in.Page)
	out = &v1.AyahListRes{
		Data: &v1.AyahListResData{
			List: make([]*v1.SuratAyatInfo, 0),
			Page: &common.PageResponse{},
		},
	}

	var ayahList []*entity.SuratAyat
	query := dao.SuratAyat.Ctx(ctx)
	if in.Id > 0 {
		query = query.Where(dao.SuratAyat.Columns().Id, in.Id)
	}
	if gconv.Int(in.SurahId) > 0 {
		query = query.Where(dao.SuratAyat.Columns().SurahId, in.SurahId)
	}
	if gconv.Int(in.JuzId) > 0 {
		query = query.Where(dao.SuratAyat.Columns().Juz, in.JuzId)
	}
	if gconv.Int(in.PageNumber) > 0 {
		query = query.Where(dao.SuratAyat.Columns().Page, in.Page.Page)
	}
	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&ayahList)

	if err != nil || len(ayahList) == 0 {
		return out
	}

	dataList := make([]*v1.SuratAyatInfo, 0, len(ayahList))
	for _, quran := range ayahList {
		transfer := &v1.SuratAyatInfo{}
		gconv.Struct(quran, transfer)
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) AyahReadRecord(ctx context.Context, in *model.AyahReadRecordInput) (out []*model.AyahReadRecordOutput) {

	//headerMap := grpcx.Ctx.IncomingMap(ctx)
	//g.Log().Line().Debug(ctx, in, headerMap.Map())

	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	SuratDaftar := s.QuerySurahByAyahId(int(in.AyahId))
	rcdData := &do.SurahReadRecord{
		UserId:    uid,
		AyahId:    in.AyahId,
		SurahName: SuratDaftar.NamaLatin,
		IsUserOp:  in.IsUserOp,
	}
	_, err = dao.SurahReadRecord.Ctx(ctx).Data(rcdData).Insert()
	if err != nil {
		g.Log().Error(ctx, "记录SurahReadRecord失败:", err)
		return
	}
	return out
}

func (s *sIslamic) AyahReadCollectList(ctx context.Context, in *v1.AyahReadCollectListReq) (out *v1.AyahReadCollectListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.AyahReadCollectListRes{
		Data: &v1.AyahReadCollectListResData{
			List: make([]*v1.ReadInfo, 0),
			Page: &common.PageResponse{},
		},
	}

	var collectList []*entity.SurahReadCollect
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	query := dao.SurahReadCollect.Ctx(ctx)
	query = query.Where(dao.SurahReadCollect.Columns().UserId, uid)

	total, _ := query.Count()
	err = query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&collectList)

	if err != nil || len(collectList) == 0 {
		return out
	}
	dataList := make([]*v1.ReadInfo, 0, len(collectList))
	for _, ayah := range collectList {
		ayahInfo := s.QueryAyahByAyahId(int(ayah.Id))
		surahInfo := s.QuerySurahByAyahId(int(ayah.Id))
		transfer := &v1.ReadInfo{}
		gconv.Struct(ayah, transfer)
		transfer.SurahId = uint32(surahInfo.Id)
		transfer.JuzId = uint32(ayahInfo.Juz)

		transfer.Arti = surahInfo.Arti
		transfer.Ayahs = uint32(surahInfo.JumlahAyat)
		transfer.FirstWord = surahInfo.Nama

		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) AyahReadCollect(ctx context.Context, in *model.AyahReadCollectInput) (out *model.AyahReadCollectOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该经文
	collectCount, err := dao.SurahReadCollect.Ctx(ctx).Where(dao.SurahReadCollect.Columns().UserId, uid).
		Where(dao.SurahReadCollect.Columns().AyahId, in.AyahId).
		Count()
	if collectCount == 0 {
		SuratDaftar := s.QuerySurahByAyahId(int(in.AyahId))
		rcdData := &do.SurahReadCollect{
			UserId:    uid,
			AyahId:    in.AyahId,
			SurahName: SuratDaftar.NamaLatin,
		}
		_, err = dao.SurahReadCollect.Ctx(ctx).Data(rcdData).Insert()
		if err != nil {
			g.Log().Error(ctx, "记录AyahReadCollectStatus失败:", err)
			return
		}
	} else {
		// 删除收藏记录
		_, err = dao.SurahReadCollect.Ctx(ctx).Where(dao.SurahReadCollect.Columns().UserId, uid).Where(dao.SurahReadCollect.Columns().AyahId, in.AyahId).
			Delete()
	}

	return out
}

func (s *sIslamic) CheckAyahReadCollectStatus(ctx context.Context, in *model.CheckAyahReadCollectStatusInput) (out *model.CheckAyahReadCollectStatusOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该经文
	collectCount, err := dao.SurahReadCollect.Ctx(ctx).Where(dao.SurahReadCollect.Columns().UserId, uid).
		Where(dao.SurahReadCollect.Columns().AyahId, in.AyahId).
		Count()
	if err != nil {
		return out
	}
	if collectCount == 0 {
		out = &model.CheckAyahReadCollectStatusOutput{
			IsCollect: 0,
		}
	} else {
		out = &model.CheckAyahReadCollectStatusOutput{
			IsCollect: 1,
		}
	}

	return out
}
func (s *sIslamic) AyahReadRecordList(ctx context.Context, in *v1.AyahReadRecordListReq) (out *v1.AyahReadRecordListRes) {

	s.setDefaultPage(ctx, &in.Page)
	out = &v1.AyahReadRecordListRes{
		Data: &v1.AyahReadRecordListResData{
			List: make([]*v1.ReadInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var recordList []*entity.SurahReadRecord
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	query := dao.SurahReadRecord.Ctx(ctx)
	query = query.Where(dao.SurahReadRecord.Columns().UserId, uid)

	total, _ := query.Count()
	err = query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&recordList)
	if err != nil || len(recordList) == 0 {
		return out
	}

	dataList := make([]*v1.ReadInfo, 0, len(recordList))
	for _, ayah := range recordList {
		ayahInfo := s.QueryAyahByAyahId(int(ayah.Id))
		surahInfo := s.QuerySurahByAyahId(int(ayah.Id))
		transfer := &v1.ReadInfo{}
		gconv.Struct(ayah, transfer)
		transfer.SurahId = uint32(surahInfo.Id)
		transfer.JuzId = uint32(ayahInfo.Juz)

		transfer.Arti = surahInfo.Arti
		transfer.Ayahs = uint32(surahInfo.JumlahAyat)
		transfer.FirstWord = surahInfo.Nama

		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList

	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

// ---------news分类相关逻辑---------------------------------------

func (s *sIslamic) QueryCateLanguageByIdAndLanId(CategoryId uint, languageId uint) (out *entity.NewsCategoryLanguage) {
	// 查询SurahId
	var newsCategoryLanguage *entity.NewsCategoryLanguage
	// 查询Surah信息
	err := dao.NewsCategoryLanguage.Ctx(context.Background()).Where(dao.NewsCategoryLanguage.Columns().CategoryId, CategoryId).Where(dao.NewsCategoryLanguage.Columns().LanguageId, languageId).Scan(&newsCategoryLanguage)
	if err != nil || newsCategoryLanguage == nil {
		return nil
	}
	return newsCategoryLanguage
}

func (s *sIslamic) NewsCategoryList(ctx context.Context, in *model.NewsCategoryListInput) (out []*model.NewsCategoryListOutput) {
	var cateList []*entity.NewsCategory

	languageId := token.GetLanguageId(ctx)
	query := dao.NewsCategory.Ctx(ctx).Where(dao.NewsCategory.Columns().Status, 1)
	query = query.Where(dao.NewsCategory.Columns().ParentId, in.Pid)
	switch languageId {
	case 0: //
		query = query.Where(dao.NewsCategory.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsCategory.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsCategory.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsCategory.Columns().IsId, 1) // 默认阿拉伯语
	}
	err := query.Scan(&cateList)
	if err != nil || len(cateList) == 0 {
		return out
	}

	for _, cate := range cateList {
		CateLanguage := s.QueryCateLanguageByIdAndLanId(cate.Id, languageId)
		one := &model.NewsCategoryListOutput{
			Id:         uint32(cate.Id),
			ParentId:   uint32(cate.ParentId),
			LanguageId: uint32(languageId),
			Name:       CateLanguage.Name,
			CoverImgs:  cate.CoverImgs,
		}
		out = append(out, one)
	}

	return out
}

func (s *sIslamic) QueryTopicLanguageByIdAndLanId(CategoryId uint, languageId uint) (out *entity.NewsTopicLanguage) {
	// 查询SurahId
	var newsTopicLanguage *entity.NewsTopicLanguage
	// 查询Surah信息
	err := dao.NewsTopicLanguage.Ctx(context.Background()).Where(dao.NewsTopicLanguage.Columns().TopicId, CategoryId).Where(dao.NewsTopicLanguage.Columns().LanguageId, languageId).Scan(&newsTopicLanguage)
	if err != nil || newsTopicLanguage == nil {
		return nil
	}
	return newsTopicLanguage
}
func (s *sIslamic) NewsTopicList(ctx context.Context, in *model.NewsTopicListInput) (out []*model.NewsTopicListOutput) {
	var cateList []*entity.NewsTopic
	query := dao.NewsTopic.Ctx(ctx).Where(dao.NewsTopic.Columns().Status, 1)
	languageId := token.GetLanguageId(ctx)
	switch languageId {
	case 0: //
		query = query.Where(dao.NewsTopic.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsTopic.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsTopic.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsTopic.Columns().IsId, 1) // 默认阿拉伯语
	}
	err := query.Scan(&cateList)
	if err != nil || len(cateList) == 0 {
		return out
	}
	for _, cate := range cateList {
		CateLanguage := s.QueryTopicLanguageByIdAndLanId(cate.Id, languageId)
		one := &model.NewsTopicListOutput{
			Id:         uint32(cate.Id),
			LanguageId: uint32(languageId),
			Name:       CateLanguage.Name,
			ShortName:  CateLanguage.ShortName,
			TopicImgs:  cate.TopicImgs,
		}
		out = append(out, one)
	}

	return out
}

func (s *sIslamic) NewsListByCateId(ctx context.Context, in *v1.NewsListByCateIdReq) (out *v1.NewsListByCateIdRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.NewsListByCateIdRes{
		Data: &v1.NewsListByCateIdResData{
			List: make([]*v1.ArticleInfo, 0),
			Page: &common.PageResponse{},
		},
	}

	var dataList []*entity.NewsArticle
	query := dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().IsPublish, 1)
	if gconv.Int(in.IsRecommend) > 0 {
		query = query.Where(dao.NewsArticle.Columns().IsRecommend, in.IsRecommend)
	}
	if gconv.Int(in.IsTop) > 0 {
		query = query.Where(dao.NewsArticle.Columns().IsTop, in.IsTop)
	}
	languageId := token.GetLanguageId(ctx)

	switch languageId {
	case 0: //
		query = query.Where(dao.NewsArticle.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsArticle.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1) // 默认阿拉伯语
	}
	if gconv.Int(in.CateId) > 0 {
		query = query.Where(dao.NewsArticle.Columns().CategoryId, in.CateId)
	}
	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&dataList)
	if err != nil || len(dataList) == 0 {
		return out
	}

	if err != nil || len(dataList) == 0 {
		return out
	}
	//提取dataList的id 列表
	ids := make([]uint, 0, len(dataList))
	cateIds := make([]uint, 0, len(dataList))
	for _, item := range dataList {
		ids = append(ids, item.Id)
		cateIds = append(cateIds, item.CategoryId)
	}
	//news_article_language 表查询数据
	var articleLanguageList []*entity.NewsArticleLanguage
	queryLanguage := dao.NewsArticleLanguage.Ctx(ctx).WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, ids).Where(dao.NewsArticleLanguage.Columns().LanguageId, languageId)
	if in.ArticleName != "" {
		queryLanguage = queryLanguage.Where(dao.NewsArticleLanguage.Columns().Name, in.ArticleName)
	}
	queryLanguage.Scan(&articleLanguageList)

	//生成一个map，key为ArticleId，value为对应的语言数据
	articleLanguageMap := make(map[uint]*entity.NewsArticleLanguage)
	for _, item := range articleLanguageList {
		articleLanguageMap[item.ArticleId] = item
	}

	//news_category_language 表查询数据
	var cateLanguageList []*entity.NewsCategoryLanguage
	dao.NewsCategoryLanguage.Ctx(ctx).WhereIn(dao.NewsCategoryLanguage.Columns().CategoryId, cateIds).Where(dao.NewsCategoryLanguage.Columns().LanguageId, languageId).Scan(&cateLanguageList)

	//生成一个map，key为ArticleId，value为对应的语言数据
	cateLanguageMap := make(map[uint]*entity.NewsCategoryLanguage)
	for _, item := range cateLanguageList {
		cateLanguageMap[item.CategoryId] = item
	}
	rtList := make([]*v1.ArticleInfo, 0, len(dataList))
	for _, item := range dataList {
		languageData, ok := articleLanguageMap[item.Id]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for article ID: %d in language ID: %d", item.Id, languageId)
			continue
		}

		cateData, ok := cateLanguageMap[item.CategoryId]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for category ID: %d in language ID: %d", item.CategoryId, languageId)
			continue
		}
		one := &v1.ArticleInfo{
			ArticleId:        uint32(item.Id),
			LanguageId:       uint32(languageId),
			Name:             languageData.Name,
			Content:          languageData.Content,
			CategoryId:       uint32(item.CategoryId),
			CategoryName:     cateData.Name,
			CoverImgs:        item.CoverImgs,
			Author:           item.Author,
			AuthorLogo:       item.AuthorLogo,
			AuthorAuthStatus: uint32(item.AuthorAuthStatus),
			PublishTime:      item.PublishTime,
		}
		rtList = append(rtList, one)
	}
	out.Data.List = rtList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) NewsInfo(ctx context.Context, in *model.NewsInfoInput) (out []*model.NewsInfoOutput) {
	var dataList []*entity.NewsArticle
	languageId := token.GetLanguageId(ctx)
	query := dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().IsPublish, 1)
	query = query.Where(dao.NewsArticle.Columns().Id, in.ArticleId)
	switch languageId {
	case 0: //
		query = query.Where(dao.NewsArticle.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsArticle.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1) // 默认阿拉伯语
	}
	err := query.Scan(&dataList)
	if err != nil || len(dataList) == 0 {
		return out
	}
	//提取dataList的id 列表
	ids := make([]uint, 0, len(dataList))
	cateIds := make([]uint, 0, len(dataList))
	for _, item := range dataList {
		ids = append(ids, item.Id)
		cateIds = append(cateIds, item.CategoryId)
	}
	//news_article_language 表查询数据
	var articleLanguageList []*entity.NewsArticleLanguage
	dao.NewsArticleLanguage.Ctx(ctx).WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, ids).Where(dao.NewsArticleLanguage.Columns().LanguageId, languageId).Scan(&articleLanguageList)

	//生成一个map，key为ArticleId，value为对应的语言数据
	articleLanguageMap := make(map[uint]*entity.NewsArticleLanguage)
	for _, item := range articleLanguageList {
		articleLanguageMap[item.ArticleId] = item
	}

	//news_category_language 表查询数据
	var cateLanguageList []*entity.NewsCategoryLanguage
	dao.NewsCategoryLanguage.Ctx(ctx).WhereIn(dao.NewsCategoryLanguage.Columns().CategoryId, cateIds).Where(dao.NewsCategoryLanguage.Columns().LanguageId, languageId).Scan(&cateLanguageList)

	//生成一个map，key为ArticleId，value为对应的语言数据
	cateLanguageMap := make(map[uint]*entity.NewsCategoryLanguage)
	for _, item := range cateLanguageList {
		cateLanguageMap[item.CategoryId] = item
	}

	for _, item := range dataList {
		languageData, ok := articleLanguageMap[item.Id]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for article ID: %d in language ID: %d", item.Id, languageId)
			continue
		}

		cateData, ok := cateLanguageMap[item.CategoryId]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for category ID: %d in language ID: %d", item.CategoryId, languageId)
			continue
		}
		one := &model.NewsInfoOutput{
			ArticleId:        uint32(item.Id),
			LanguageId:       uint32(languageId),
			Name:             languageData.Name,
			Content:          languageData.Content,
			CategoryId:       uint32(item.CategoryId),
			CategoryName:     cateData.Name,
			CoverImgs:        item.CoverImgs,
			Author:           item.Author,
			AuthorLogo:       item.AuthorLogo,
			AuthorAuthStatus: uint32(item.AuthorAuthStatus),
			PublishTime:      item.PublishTime,
		}
		out = append(out, one)
	}
	return out
}

func (s *sIslamic) getTopicArticleList(ctx context.Context, ids []uint, languageId uint32) (out map[uint]*model.NewsListByTopicIdOutput) {
	out = make(map[uint]*model.NewsListByTopicIdOutput)
	var dataList []*entity.NewsArticle
	query := dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().IsPublish, 1).WhereIn(
		dao.NewsArticle.Columns().Id, ids)

	switch languageId {
	case 0: //
		query = query.Where(dao.NewsArticle.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsArticle.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1) // 默认阿拉伯语
	}
	err := query.Scan(&dataList)
	if err != nil || len(dataList) == 0 {
		return out
	}

	//提取cateIds列表
	cateIds := make([]uint, 0, len(dataList))
	for _, item := range dataList {
		cateIds = append(cateIds, item.CategoryId)
	}

	//news_article_language 表查询数据
	var articleLanguageList []*entity.NewsArticleLanguage
	dao.NewsArticleLanguage.Ctx(ctx).WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, ids).Where(dao.NewsArticleLanguage.Columns().LanguageId, languageId).Scan(&articleLanguageList)
	articleLanguageMap := make(map[uint]*entity.NewsArticleLanguage)
	for _, item := range articleLanguageList {
		articleLanguageMap[item.ArticleId] = item
	}
	////news_category_language 表查询数据
	var cateLanguageList []*entity.NewsCategoryLanguage
	dao.NewsCategoryLanguage.Ctx(ctx).WhereIn(dao.NewsCategoryLanguage.Columns().CategoryId, cateIds).Where(dao.NewsCategoryLanguage.Columns().LanguageId, languageId).Scan(&cateLanguageList)
	//生成一个map，key为ArticleId，value为对应的语言数据
	cateLanguageMap := make(map[uint]*entity.NewsCategoryLanguage)
	for _, item := range cateLanguageList {
		cateLanguageMap[item.CategoryId] = item
	}

	for _, item := range dataList {
		languageData, ok := articleLanguageMap[item.Id]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for article ID: %d in language ID: %d", item.Id, languageId)
			continue
		}

		cateData, ok := cateLanguageMap[item.CategoryId]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for category ID: %d in language ID: %d", item.CategoryId, languageId)
			continue
		}
		out[item.Id] = &model.NewsListByTopicIdOutput{
			ArticleId:        uint32(item.Id),
			LanguageId:       languageId,
			Name:             languageData.Name,
			Content:          languageData.Content,
			CategoryId:       uint32(item.CategoryId),
			CategoryName:     cateData.Name,
			CoverImgs:        item.CoverImgs,
			Author:           item.Author,
			AuthorLogo:       item.AuthorLogo,
			AuthorAuthStatus: uint32(item.AuthorAuthStatus),
			PublishTime:      item.PublishTime,
		}
	}
	return out
}

func (s *sIslamic) getTopicInfo(ctx context.Context, topicId uint32, languageId uint32) (topicName string) {
	//news_article_language 表查询数据
	var topicLanguage *entity.NewsTopicLanguage
	dao.NewsTopicLanguage.Ctx(ctx).WhereIn(dao.NewsTopicLanguage.Columns().TopicId, topicId).Where(dao.NewsTopicLanguage.Columns().LanguageId, languageId).Scan(&topicLanguage)
	if topicLanguage != nil {
		topicName = topicLanguage.Name
	} else {
		topicName = ""
	}
	return topicName
}

func (s *sIslamic) NewsListByTopicId(ctx context.Context, in *v1.NewsListByTopicIdReq) (out *v1.NewsListByTopicIdRes) {
	languageId := token.GetLanguageId(ctx)

	s.setDefaultPage(ctx, &in.Page)
	out = &v1.NewsListByTopicIdRes{
		Data: &v1.NewsListByTopicIdResData{
			List: make([]*v1.ArticleInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var dataList []*entity.NewsTopicArticle
	query := dao.NewsTopicArticle.Ctx(ctx).Where(dao.NewsTopicArticle.Columns().TopicId, in.TopicId).Where(dao.NewsTopic.Columns().DeleteTime, 0)

	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&dataList)
	if err != nil || len(dataList) == 0 {
		return out
	}

	//提取dataList的id 列表
	articleIds := make([]uint, 0, len(dataList))
	for _, item := range dataList {
		articleIds = append(articleIds, item.ArticleId)
	}
	TopicName := s.getTopicInfo(ctx, in.TopicId, uint32(languageId))
	ArticleInfo := s.getTopicArticleList(ctx, articleIds, uint32(languageId))

	outList := make([]*v1.ArticleInfo, 0, len(dataList))
	for _, item := range dataList {
		if _, ok := ArticleInfo[item.ArticleId]; !ok {
			// 如果没有对应的文章数据，跳过该文章
			g.Log().Warningf(ctx, "No article data found for article ID: %d in topic ID: %d", item.ArticleId, in.TopicId)
			continue
		}
		one := &v1.ArticleInfo{
			ArticleId:  uint32(item.Id),
			LanguageId: uint32(languageId),
			Name:       item.ArticleName,

			Content:          ArticleInfo[item.Id].Content,
			CategoryId:       ArticleInfo[item.Id].CategoryId,
			CategoryName:     ArticleInfo[item.Id].CategoryName,
			CoverImgs:        ArticleInfo[item.Id].CoverImgs,
			Author:           ArticleInfo[item.Id].Author,
			AuthorLogo:       ArticleInfo[item.Id].AuthorLogo,
			AuthorAuthStatus: ArticleInfo[item.Id].AuthorAuthStatus,
			PublishTime:      ArticleInfo[item.Id].PublishTime,
			//TopicName: TopicName,
		}
		outList = append(outList, one)
	}
	out.Data.TopicName = TopicName
	out.Data.List = outList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) NewsHotArticleIds(ctx context.Context, limit int) (articleIds []uint) {
	//最近24小时内发布的文章 阅读量top3
	var viewList []*entity.NewsArticleView
	//获取当前时间之前24小时的时间戳
	twentyFourHoursAgo := gtime.Now().Add(-24 * time.Hour).Timestamp()
	dao.NewsArticleView.Ctx(ctx).WhereGTE(dao.NewsArticleView.Columns().CreateTime, twentyFourHoursAgo).Scan(&viewList)
	//init view map
	viewMap := make(map[uint]int)
	for _, item := range viewList {
		//if viewmap contain item.ArticleId, then add view count
		if count, ok := viewMap[item.ArticleId]; ok {
			viewMap[item.ArticleId] = count + 1
		} else {
			viewMap[item.ArticleId] = 1
		}
	}
	//viewMap 排序 取前三个值
	// Sort viewMap by values and get the top three keys
	type kv struct {
		Key   uint
		Value int
	}
	// Convert map to slice of key-value pairs
	var sortedViews []kv
	for k, v := range viewMap {
		sortedViews = append(sortedViews, kv{Key: uint(k), Value: v})
	}
	// Sort the slice by Value in descending order
	sort.Slice(sortedViews, func(i, j int) bool {
		return sortedViews[i].Value > sortedViews[j].Value
	})
	// Extract the top three keys
	for i := 0; i < len(sortedViews) && i < limit; i++ {
		articleIds = append(articleIds, sortedViews[i].Key)
	}
	return articleIds
}
func (s *sIslamic) NewsHotList(ctx context.Context, in *v1.NewsHotListReq) (out *v1.NewsHotListRes) {
	s.setDefaultPage(ctx, &in.Page)
	languageId := token.GetLanguageId(ctx)

	out = &v1.NewsHotListRes{
		Data: &v1.NewsHotListResData{
			List: make([]*v1.ArticleInfo, 0),
			Page: &common.PageResponse{},
		},
	}

	var dataList []*entity.NewsArticle
	query := dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().IsPublish, 1)

	if in.IsHot == 1 {
		articleIds := s.NewsHotArticleIds(ctx, 3)
		query = query.WhereIn(dao.NewsArticle.Columns().Id, articleIds)
	} else {
		articleIds := s.NewsHotArticleIds(ctx, 20)
		query = query.WhereIn(dao.NewsArticle.Columns().Id, articleIds)
	}

	switch languageId {
	case 0: //
		query = query.Where(dao.NewsArticle.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsArticle.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1) // 默认阿拉伯语
	}

	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&dataList)
	if err != nil || len(dataList) == 0 {
		return out
	}
	//提取dataList的id 列表
	ids := make([]uint, 0, len(dataList))
	cateIds := make([]uint, 0, len(dataList))
	for _, item := range dataList {
		ids = append(ids, item.Id)
		cateIds = append(cateIds, item.CategoryId)
	}
	//news_article_language 表查询数据
	var articleLanguageList []*entity.NewsArticleLanguage
	dao.NewsArticleLanguage.Ctx(ctx).WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, ids).Where(dao.NewsArticleLanguage.Columns().LanguageId, languageId).Scan(&articleLanguageList)

	//生成一个map，key为ArticleId，value为对应的语言数据
	articleLanguageMap := make(map[uint]*entity.NewsArticleLanguage)
	for _, item := range articleLanguageList {
		articleLanguageMap[item.ArticleId] = item
	}

	//news_category_language 表查询数据
	var cateLanguageList []*entity.NewsCategoryLanguage
	dao.NewsCategoryLanguage.Ctx(ctx).WhereIn(dao.NewsCategoryLanguage.Columns().CategoryId, cateIds).Where(dao.NewsCategoryLanguage.Columns().LanguageId, languageId).Scan(&cateLanguageList)

	//生成一个map，key为ArticleId，value为对应的语言数据
	cateLanguageMap := make(map[uint]*entity.NewsCategoryLanguage)
	for _, item := range cateLanguageList {
		cateLanguageMap[item.CategoryId] = item
	}

	outList := make([]*v1.ArticleInfo, 0, len(dataList))
	for _, item := range dataList {
		languageData, ok := articleLanguageMap[item.Id]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for article ID: %d in language ID: %d", item.Id, languageId)
			continue
		}

		cateData, ok := cateLanguageMap[item.CategoryId]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for category ID: %d in language ID: %d", item.CategoryId, languageId)
			continue
		}
		one := &v1.ArticleInfo{
			ArticleId:        uint32(item.Id),
			LanguageId:       uint32(languageId),
			Name:             languageData.Name,
			Content:          languageData.Content,
			CategoryId:       uint32(item.CategoryId),
			CategoryName:     cateData.Name,
			CoverImgs:        item.CoverImgs,
			Author:           item.Author,
			AuthorLogo:       item.AuthorLogo,
			AuthorAuthStatus: uint32(item.AuthorAuthStatus),
			PublishTime:      item.PublishTime,
		}
		outList = append(outList, one)
	}
	out.Data.List = outList

	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) NewsCollectArticleIds(ctx context.Context, limit int) (articleIds []uint) {
	var collectList []*entity.NewsArticleCollect
	uid, _ := token.GetUserIdByToken(ctx)
	dao.NewsArticleCollect.Ctx(ctx).Where(dao.NewsArticleCollect.Columns().UserId, uid).Scan(&collectList)
	for _, item := range collectList {
		articleIds = append(articleIds, item.ArticleId)
	}
	return articleIds
}
func (s *sIslamic) NewsCollectList(ctx context.Context, in *v1.NewsCollectReq) (out *v1.NewsCollectRes) {
	languageId := token.GetLanguageId(ctx)

	s.setDefaultPage(ctx, &in.Page)
	out = &v1.NewsCollectRes{
		Data: &v1.NewsCollectResData{
			List: make([]*v1.ArticleInfo, 0),
			Page: &common.PageResponse{},
		},
	}
	var dataList []*entity.NewsArticle
	query := dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().IsPublish, 1)

	articleIds := s.NewsCollectArticleIds(ctx, 20)
	query = query.WhereIn(dao.NewsArticle.Columns().Id, articleIds)

	switch languageId {
	case 0: //
		query = query.Where(dao.NewsArticle.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsArticle.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsArticle.Columns().IsId, 1) // 默认阿拉伯语
	}
	total, _ := query.Count()
	err := query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&dataList)
	if err != nil || len(dataList) == 0 {
		return out
	}

	//提取dataList的id 列表
	ids := make([]uint, 0, len(dataList))
	cateIds := make([]uint, 0, len(dataList))
	for _, item := range dataList {
		ids = append(ids, item.Id)
		cateIds = append(cateIds, item.CategoryId)
	}
	//news_article_language 表查询数据
	var articleLanguageList []*entity.NewsArticleLanguage
	dao.NewsArticleLanguage.Ctx(ctx).WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, ids).Where(dao.NewsArticleLanguage.Columns().LanguageId, languageId).Scan(&articleLanguageList)

	//生成一个map，key为ArticleId，value为对应的语言数据
	articleLanguageMap := make(map[uint]*entity.NewsArticleLanguage)
	for _, item := range articleLanguageList {
		articleLanguageMap[item.ArticleId] = item
	}

	//news_category_language 表查询数据
	var cateLanguageList []*entity.NewsCategoryLanguage
	dao.NewsCategoryLanguage.Ctx(ctx).WhereIn(dao.NewsCategoryLanguage.Columns().CategoryId, cateIds).Where(dao.NewsCategoryLanguage.Columns().LanguageId, languageId).Scan(&cateLanguageList)

	//生成一个map，key为ArticleId，value为对应的语言数据
	cateLanguageMap := make(map[uint]*entity.NewsCategoryLanguage)
	for _, item := range cateLanguageList {
		cateLanguageMap[item.CategoryId] = item
	}
	outList := make([]*v1.ArticleInfo, 0, len(dataList))
	for _, item := range dataList {
		languageData, ok := articleLanguageMap[item.Id]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for article ID: %d in language ID: %d", item.Id, languageId)
			continue
		}

		cateData, ok := cateLanguageMap[item.CategoryId]
		if !ok {
			// 如果没有对应的语言数据，跳过该文章
			g.Log().Warningf(ctx, "No language data found for category ID: %d in language ID: %d", item.CategoryId, languageId)
			continue
		}
		one := &v1.ArticleInfo{
			ArticleId:        uint32(item.Id),
			LanguageId:       uint32(languageId),
			Name:             languageData.Name,
			Content:          languageData.Content,
			CategoryId:       uint32(item.CategoryId),
			CategoryName:     cateData.Name,
			CoverImgs:        item.CoverImgs,
			Author:           item.Author,
			AuthorLogo:       item.AuthorLogo,
			AuthorAuthStatus: uint32(item.AuthorAuthStatus),
			PublishTime:      item.PublishTime,
		}
		outList = append(outList, one)
	}
	out.Data.List = outList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) NewsCollectStatusCheck(ctx context.Context, in *model.NewsCollectStatusCheckInput) (out *model.NewsCollectStatusCheckOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该文章
	collectCount, err := dao.NewsArticleCollect.Ctx(ctx).Where(dao.NewsArticleCollect.Columns().UserId, uid).
		Where(dao.NewsArticleCollect.Columns().ArticleId, in.ArticleId).
		Count()
	if err != nil {
		return out
	}
	if collectCount == 0 {
		out = &model.NewsCollectStatusCheckOutput{
			IsCollect: 0,
		}
	} else {
		out = &model.NewsCollectStatusCheckOutput{
			IsCollect: 1,
		}
	}
	return out
}

func (s *sIslamic) QueryArticleByArticleId(article int, language int) (out *entity.NewsArticleLanguage) {
	// 查询SurahId
	var newsArticleLanguage *entity.NewsArticleLanguage
	dao.NewsArticleLanguage.Ctx(context.Background()).Where(dao.NewsArticleLanguage.Columns().ArticleId, article).Where(dao.NewsArticleLanguage.Columns().LanguageId, language).Scan(&newsArticleLanguage)
	return newsArticleLanguage
}
func (s *sIslamic) NewsCollectOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该文章
	collectCount, err := dao.NewsArticleCollect.Ctx(ctx).Where(dao.NewsArticleCollect.Columns().UserId, uid).
		Where(dao.NewsArticleCollect.Columns().ArticleId, in.ArticleId).
		Count()
	if collectCount == 0 {
		articleName := s.QueryArticleByArticleId(int(in.ArticleId), 2)
		rcdData := &do.NewsArticleCollect{
			UserId:      uid,
			ArticleId:   in.ArticleId,
			ArticleName: articleName.Name,
		}
		_, err = dao.NewsArticleCollect.Ctx(ctx).Data(rcdData).Insert()
		if err != nil {
			g.Log().Error(ctx, "记录NewsArticleCollect失败:", err)
			return
		}
	} else {
		// 删除收藏记录
		_, err = dao.NewsArticleCollect.Ctx(ctx).Where(dao.NewsArticleCollect.Columns().UserId, uid).Where(dao.NewsArticleCollect.Columns().ArticleId, in.ArticleId).
			Delete()
	}

	return out
}

func (s *sIslamic) NewsViewOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该文章

	articleName := s.QueryArticleByArticleId(int(in.ArticleId), 2)
	rcdData := &do.NewsArticleView{
		UserId:      uid,
		ArticleId:   in.ArticleId,
		ArticleName: articleName.Name,
	}
	_, err = dao.NewsArticleView.Ctx(ctx).Data(rcdData).Insert()
	if err != nil {
		g.Log().Error(ctx, "记录NewsArticleView失败:", err)
		return
	}

	return out
}

func (s *sIslamic) NewsShareOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该文章

	articleName := s.QueryArticleByArticleId(int(in.ArticleId), 2)
	rcdData := &do.NewsArticleShare{
		UserId:      uid,
		ArticleId:   in.ArticleId,
		ArticleName: articleName.Name,
	}
	_, err = dao.NewsArticleShare.Ctx(ctx).Data(rcdData).Insert()
	if err != nil {
		g.Log().Error(ctx, "记录NewsArticleShare失败:", err)
		return
	}

	return out
}

// --------doa-----
func (s *sIslamic) DoaReadCollectList(ctx context.Context, in *v1.DoaReadCollectListReq) (out *v1.DoaReadCollectListRes) {
	s.setDefaultPage(ctx, &in.Page)
	out = &v1.DoaReadCollectListRes{
		Data: &v1.DoaReadCollectListResData{
			List: make([]*v1.DoaReadInfo, 0),
			Page: &common.PageResponse{},
		},
	}

	var collectList []*entity.DoaReadCollect
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	query := dao.DoaReadCollect.Ctx(ctx)
	query = query.Where(dao.DoaReadCollect.Columns().UserId, uid)

	total, _ := query.Count()
	err = query.Page(int(in.Page.Page), int(in.Page.Size)).Order("id desc").Scan(&collectList)

	if err != nil || len(collectList) == 0 {
		return out
	}
	dataList := make([]*v1.DoaReadInfo, 0, len(collectList))
	for _, ayah := range collectList {
		transfer := &v1.DoaReadInfo{}
		gconv.Struct(ayah, transfer)
		transfer.PName = ayah.PName
		dataList = append(dataList, transfer)
	}
	out.Data.List = dataList
	out.Data.Page = &common.PageResponse{
		Page:  in.Page.Page,
		Size:  in.Page.Size,
		Total: int32(total),
	}
	return out
}

func (s *sIslamic) QueryDoaByBaccanId(baccanId int) (doaInfo *entity.NewsDoa, doaBaccanInfo *entity.NewsDoaBacaan) {
	// 查询SurahId
	var newsDoaBacaan *entity.NewsDoaBacaan
	err := dao.NewsDoaBacaan.Ctx(context.Background()).Where(dao.NewsDoaBacaan.Columns().Id, baccanId).Scan(&newsDoaBacaan)
	if err != nil || newsDoaBacaan == nil {
		return nil, nil
	}
	// 查询Surah信息
	var newsDoa *entity.NewsDoa
	err = dao.NewsDoa.Ctx(context.Background()).Where(dao.NewsDoa.Columns().Id, newsDoaBacaan.DoaId).Scan(&newsDoa)
	if err != nil || newsDoa == nil {
		return nil, newsDoaBacaan
	}
	return newsDoa, newsDoaBacaan
}

func (s *sIslamic) QueryWiridByBaccanId(baccanId int) (wiridInfo *entity.NewsWirid, wiridBaccanInfo *entity.NewsWiridBacaan) {
	var newsWiridBacaan *entity.NewsWiridBacaan
	err := dao.NewsWiridBacaan.Ctx(context.Background()).Where(dao.NewsWiridBacaan.Columns().Id, baccanId).Scan(&newsWiridBacaan)
	if err != nil || newsWiridBacaan == nil {
		return nil, nil
	}

	var newsWirid *entity.NewsWirid
	err = dao.NewsWirid.Ctx(context.Background()).Where(dao.NewsWirid.Columns().Id, newsWiridBacaan.WiridId).Scan(&newsWirid)
	if err != nil || newsWirid == nil {
		return nil, newsWiridBacaan
	}
	return newsWirid, newsWiridBacaan
}

func (s *sIslamic) DoaReadCollect(ctx context.Context, in *model.DoaReadCollectInput) (out *model.AyahReadCollectOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该doa
	collectCount, err := dao.DoaReadCollect.Ctx(ctx).Where(dao.DoaReadCollect.Columns().UserId, uid).Where(dao.DoaReadCollect.Columns().BaccanId, in.BaccanId).Where(dao.DoaReadCollect.Columns().Types, in.Types).Count()
	if collectCount == 0 {
		if in.Types == 1 {
			doaInfo, doaBaccanInfo := s.QueryDoaByBaccanId(int(in.BaccanId))
			if doaInfo == nil || doaBaccanInfo == nil {
				g.Log().Error(ctx, "查询Wirid信息失败，可能是BaccanId错误:", in.BaccanId)
				return
			}
			rcdData := &do.DoaReadCollect{
				UserId:     uid,
				Types:      in.Types,
				PId:        doaInfo.Id,
				PName:      doaInfo.Name,
				BaccanId:   in.BaccanId,
				BaccanName: doaBaccanInfo.Name,
			}
			_, err = dao.DoaReadCollect.Ctx(ctx).Data(rcdData).Insert()
			if err != nil {
				g.Log().Error(ctx, "记录DoaReadCollectStatus doa失败:", err)
				return
			}
		} else {
			wiridInfo, wiridBaccanInfo := s.QueryWiridByBaccanId(int(in.BaccanId))
			if wiridInfo == nil || wiridBaccanInfo == nil {
				g.Log().Error(ctx, "查询Wirid信息失败，可能是BaccanId错误:", in.BaccanId)
				return
			}
			rcdData := &do.DoaReadCollect{
				UserId:     uid,
				Types:      in.Types,
				PId:        wiridInfo.Id,
				PName:      wiridInfo.Name,
				BaccanId:   in.BaccanId,
				BaccanName: wiridBaccanInfo.Name,
			}
			_, err = dao.DoaReadCollect.Ctx(ctx).Data(rcdData).Insert()
			if err != nil {
				g.Log().Error(ctx, "记录DoaReadCollectStatus wirid失败:", err)
				return
			}
		}
	} else {
		// 删除收藏记录
		_, err = dao.DoaReadCollect.Ctx(ctx).Where(dao.DoaReadCollect.Columns().UserId, uid).Where(dao.DoaReadCollect.Columns().BaccanId, in.BaccanId).Where(dao.DoaReadCollect.Columns().Types, in.Types).Delete()
	}

	return out
}

func (s *sIslamic) CheckDoaReadCollectStatus(ctx context.Context, in *model.CheckDoaReadCollectStatusInput) (out *model.CheckAyahReadCollectStatusOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该doa
	collectCount, err := dao.DoaReadCollect.Ctx(ctx).Where(dao.DoaReadCollect.Columns().UserId, uid).Where(dao.DoaReadCollect.Columns().BaccanId, in.BaccanId).Where(dao.DoaReadCollect.Columns().Types, in.Types).Count()
	if err != nil {
		return out
	}
	if collectCount == 0 {
		out = &model.CheckAyahReadCollectStatusOutput{
			IsCollect: 0,
		}
	} else {
		out = &model.CheckAyahReadCollectStatusOutput{
			IsCollect: 1,
		}
	}

	return out
}
