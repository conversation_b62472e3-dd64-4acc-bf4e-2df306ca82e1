// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.27.2
// source: islamic/v1/faq.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FaqService_FaqCategoryList_FullMethodName = "/islamic.v1.FaqService/FaqCategoryList"
	FaqService_FaqListByCateId_FullMethodName = "/islamic.v1.FaqService/FaqListByCateId"
	FaqService_FaqOne_FullMethodName          = "/islamic.v1.FaqService/FaqOne"
	FaqService_FaqMedia_FullMethodName        = "/islamic.v1.FaqService/FaqMedia"
)

// FaqServiceClient is the client API for FaqService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FaqServiceClient interface {
	// FAQ分类列表
	FaqCategoryList(ctx context.Context, in *FaqCateListReq, opts ...grpc.CallOption) (*FaqCateListRes, error)
	// FAQ列表通过分类ID
	FaqListByCateId(ctx context.Context, in *FaqListByCateIdReq, opts ...grpc.CallOption) (*FaqListByCateIdRes, error)
	// FAQ详情
	FaqOne(ctx context.Context, in *FaqOneReq, opts ...grpc.CallOption) (*FaqOneRes, error)
	// 社交媒体
	FaqMedia(ctx context.Context, in *FaqMediaReq, opts ...grpc.CallOption) (*FaqMediaRes, error)
}

type faqServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFaqServiceClient(cc grpc.ClientConnInterface) FaqServiceClient {
	return &faqServiceClient{cc}
}

func (c *faqServiceClient) FaqCategoryList(ctx context.Context, in *FaqCateListReq, opts ...grpc.CallOption) (*FaqCateListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaqCateListRes)
	err := c.cc.Invoke(ctx, FaqService_FaqCategoryList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faqServiceClient) FaqListByCateId(ctx context.Context, in *FaqListByCateIdReq, opts ...grpc.CallOption) (*FaqListByCateIdRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaqListByCateIdRes)
	err := c.cc.Invoke(ctx, FaqService_FaqListByCateId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faqServiceClient) FaqOne(ctx context.Context, in *FaqOneReq, opts ...grpc.CallOption) (*FaqOneRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaqOneRes)
	err := c.cc.Invoke(ctx, FaqService_FaqOne_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faqServiceClient) FaqMedia(ctx context.Context, in *FaqMediaReq, opts ...grpc.CallOption) (*FaqMediaRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaqMediaRes)
	err := c.cc.Invoke(ctx, FaqService_FaqMedia_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FaqServiceServer is the server API for FaqService service.
// All implementations must embed UnimplementedFaqServiceServer
// for forward compatibility.
type FaqServiceServer interface {
	// FAQ分类列表
	FaqCategoryList(context.Context, *FaqCateListReq) (*FaqCateListRes, error)
	// FAQ列表通过分类ID
	FaqListByCateId(context.Context, *FaqListByCateIdReq) (*FaqListByCateIdRes, error)
	// FAQ详情
	FaqOne(context.Context, *FaqOneReq) (*FaqOneRes, error)
	// 社交媒体
	FaqMedia(context.Context, *FaqMediaReq) (*FaqMediaRes, error)
	mustEmbedUnimplementedFaqServiceServer()
}

// UnimplementedFaqServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFaqServiceServer struct{}

func (UnimplementedFaqServiceServer) FaqCategoryList(context.Context, *FaqCateListReq) (*FaqCateListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqCategoryList not implemented")
}
func (UnimplementedFaqServiceServer) FaqListByCateId(context.Context, *FaqListByCateIdReq) (*FaqListByCateIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqListByCateId not implemented")
}
func (UnimplementedFaqServiceServer) FaqOne(context.Context, *FaqOneReq) (*FaqOneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqOne not implemented")
}
func (UnimplementedFaqServiceServer) FaqMedia(context.Context, *FaqMediaReq) (*FaqMediaRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqMedia not implemented")
}
func (UnimplementedFaqServiceServer) mustEmbedUnimplementedFaqServiceServer() {}
func (UnimplementedFaqServiceServer) testEmbeddedByValue()                    {}

// UnsafeFaqServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FaqServiceServer will
// result in compilation errors.
type UnsafeFaqServiceServer interface {
	mustEmbedUnimplementedFaqServiceServer()
}

func RegisterFaqServiceServer(s grpc.ServiceRegistrar, srv FaqServiceServer) {
	// If the following call pancis, it indicates UnimplementedFaqServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FaqService_ServiceDesc, srv)
}

func _FaqService_FaqCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqCateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FaqService_FaqCategoryList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqCategoryList(ctx, req.(*FaqCateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaqService_FaqListByCateId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqListByCateIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqListByCateId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FaqService_FaqListByCateId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqListByCateId(ctx, req.(*FaqListByCateIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaqService_FaqOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FaqService_FaqOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqOne(ctx, req.(*FaqOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaqService_FaqMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqMediaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FaqService_FaqMedia_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqMedia(ctx, req.(*FaqMediaReq))
	}
	return interceptor(ctx, in, info, handler)
}

// FaqService_ServiceDesc is the grpc.ServiceDesc for FaqService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FaqService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.FaqService",
	HandlerType: (*FaqServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FaqCategoryList",
			Handler:    _FaqService_FaqCategoryList_Handler,
		},
		{
			MethodName: "FaqListByCateId",
			Handler:    _FaqService_FaqListByCateId_Handler,
		},
		{
			MethodName: "FaqOne",
			Handler:    _FaqService_FaqOne_Handler,
		},
		{
			MethodName: "FaqMedia",
			Handler:    _FaqService_FaqMedia_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/faq.proto",
}
