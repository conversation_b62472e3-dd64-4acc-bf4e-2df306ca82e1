package islamic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
	"halalplus/utility/page"
	"halalplus/utility/token"
)

type ControllerVideo struct {
	v1.UnimplementedVideoServiceServer
}

// PlaylistList 获取视频播放列表
func (*ControllerVideo) PlaylistList(ctx context.Context, req *v1.PlaylistListReq) (res *v1.PlaylistListRes, err error) {
	pageParams := page.NormalizePageRequest(req.Page)

	output, err := service.Video().PlaylistList(ctx, req.LanguageId, pageParams.Page, pageParams.Size)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换
	var playlistList []*v1.PlaylistItem
	err = gconv.Structs(output.List, &playlistList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.PlaylistListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.PlaylistListResData{
			List: playlistList,
			Page: pageParams.ToPageResponse(output.Total),
		},
	}, nil
}

// VideoList 获取视频列表
func (*ControllerVideo) VideoList(ctx context.Context, req *v1.VideoListReq) (res *v1.VideoListRes, err error) {
	pageParams := page.NormalizePageRequest(req.Page)

	// 输入参数
	input := &model.VideoListInput{
		LanguageId: req.LanguageId,
		CategoryId: req.CategoryId,
		PlaylistId: req.PlaylistId,
		Title:      req.Title,
		SortBy:     req.SortBy,
		SortOrder:  req.SortOrder,
		Page:       pageParams.Page,
		Size:       pageParams.Size,
	}

	output, err := service.Video().VideoList(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换
	var videoList []*v1.VideoListItem
	err = gconv.Structs(output.List, &videoList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 响应数据
	resData := &v1.VideoListResData{
		List: videoList,
		Page: pageParams.ToPageResponse(output.Total),
	}

	// 如果有播放列表信息，添加到响应中
	if output.Playlist != nil {
		resData.Playlist = &v1.PlaylistBasicInfo{
			PlaylistId: output.Playlist.PlaylistId,
			Name:       output.Playlist.Name,
		}
	}

	return &v1.VideoListRes{
		Code: 200,
		Msg:  "success",
		Data: resData,
	}, nil
}

// VideoDetail 获取视频详情
func (*ControllerVideo) VideoDetail(ctx context.Context, req *v1.VideoDetailReq) (res *v1.VideoDetailRes, err error) {
	// 获取用户ID，不判断这里的err，因为有可能不一定要登陆的
	uid, _ := token.GetUserIdByToken(ctx)

	output, err := service.Video().VideoDetail(ctx, req.VideoId, req.LanguageId, uid)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换
	var video *v1.Video
	err = gconv.Struct(output.Video, &video)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.VideoDetailRes{
		Code: 200,
		Msg:  "success",
		Data: video,
	}, nil
}

// RecommendedVideoList 获取推荐视频列表
func (*ControllerVideo) RecommendedVideoList(ctx context.Context, req *v1.RecommendedVideoListReq) (res *v1.RecommendedVideoListRes, err error) {
	pageParams := page.NormalizePageRequest(req.Page)

	// 构建输入参数
	input := &model.RecommendedVideoListInput{
		LanguageId: req.LanguageId,
		CategoryId: req.CategoryId,
		Page:       pageParams.Page,
		Size:       pageParams.Size,
	}

	output, err := service.Video().RecommendedVideoList(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换
	var videoList []*v1.VideoListItem
	err = gconv.Structs(output.List, &videoList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.RecommendedVideoListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.RecommendedVideoListResData{
			List: videoList,
			Page: pageParams.ToPageResponse(output.Total),
		},
	}, nil
}

// VideoCollect 视频收藏/取消收藏
func (*ControllerVideo) VideoCollect(ctx context.Context, req *v1.VideoCollectReq) (res *v1.VideoCollectRes, err error) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	// 构建输入参数
	input := &model.VideoCollectInput{
		UserId:  uid,
		VideoId: req.VideoId,
		IsAdd:   req.IsAdd,
	}

	err = service.Video().VideoCollect(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.VideoCollectRes{
		Code: 200,
		Msg:  "success",
	}, nil
}

// VideoCollectList 获取用户收藏的视频列表
func (*ControllerVideo) VideoCollectList(ctx context.Context, req *v1.VideoCollectListReq) (res *v1.VideoCollectListRes, err error) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	pageParams := page.NormalizePageRequest(req.Page)

	output, err := service.Video().VideoCollectList(ctx, uid, req.LanguageId, pageParams.Page, pageParams.Size)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换
	var videoList []*v1.VideoListItem
	err = gconv.Structs(output.List, &videoList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.VideoCollectListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.VideoCollectListResData{
			List: videoList,
			Page: pageParams.ToPageResponse(output.Total),
		},
	}, nil
}

func (*ControllerVideo) VideoCollectStatusCheck(ctx context.Context, req *v1.VideoCollectStatusCheckReq) (res *v1.VideoCollectStatusCheckRes, err error) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return &v1.VideoCollectStatusCheckRes{
			Code: 200,
			Msg:  "success",
			Data: &v1.VideoCollectStatusCheckData{
				IsCollect: 1,
			},
		}, nil
	}

	output, err := service.Video().VideoCollectStatusCheck(ctx, uid, req.VideoId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	isCollect := int32(1) // 默认未收藏
	if output.IsCollect > 0 {
		isCollect = 2 // 已收藏
	}

	return &v1.VideoCollectStatusCheckRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.VideoCollectStatusCheckData{
			IsCollect: isCollect,
		},
	}, nil
}
